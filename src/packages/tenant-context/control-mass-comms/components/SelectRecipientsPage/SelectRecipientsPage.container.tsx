import { FC, useCallback, useEffect,useMemo,useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDebouncedCallback } from 'use-debounce';

import { Dispatch, RootState, RootStateWithLoading } from '@/core/store';
import SelectRecipientsPageComponent
  from '@/tenant-context/control-mass-comms/components/SelectRecipientsPage/SelectRecipientsPage.component';
import { GroupEntity, ImpactResponseName, PeopleEntity, RecipientMainSelection } from '@/tenant-context/control-mass-comms/types/MassComms.types';

const DEBOUNCE_DELAY = 1000;

const SelectRecipientsPage: FC = () => {

  const selectedRecipientType = useSelector((state: RootState) => state.massComms.selectedRecipientType);
  const impactResponseItems  = useSelector((state: RootState) => state.massComms.impactResponseItems);
  const isDeliverySearchLoading  = useSelector((state: RootState) => state.massComms?.isDeliveryEntitySearchLoading);
  const searchedDeliveryEntities  = useSelector((state: RootState) => state.massComms.searchedDeliveryEntities);
  const selectedGroups = useSelector((state: RootState) => state.massComms.selectedGroups);
  const massCommsNavigation = useSelector((state: RootState) => state.massComms.massCommsNavigation);
  const selectedRecipients = useSelector((state: RootState) => state.massComms.selectedRecipients);
  const fetchInProgressGroupsList = useSelector((state: RootState) => state.massComms.fetchInProgressGroupsList);

  const {
    loadNextPagePersons: isLoadingPersons,
    loadNextPagePeopleGroups: isLoadingPeopleGroups
  } = useSelector((state: RootStateWithLoading) => state.loading.effects.massComms);

  const [personSearchCriteria, setPersonSearchCriteria] = useState<string>('');
  const [groupSearchCriteria, setGroupSearchCriteria] = useState<string>('');
  const [selectedTab, setSelectedTab] = useState<string>('individual');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [sortMethod, setSortMethod] = useState<'name:asc' | 'name:desc'>('name:asc');

  // State for tracking focus on search inputs
  const [isPersonSearchFocused, setIsPersonSearchFocused] = useState<boolean>(false);
  const [isGroupSearchFocused, setIsGroupSearchFocused] = useState<boolean>(false);

  const categoryList = useMemo(function() {
    const categories = searchedDeliveryEntities.groups
      ?.map(function(group) {
        return group?.category;
      })
      .filter(function(category): category is string {
        return typeof category === 'string' && category.length > 0;
      }) || [];

    return Array.from(new Set(categories));
  }, [searchedDeliveryEntities.groups]);

  const handleCategoryChange = useCallback(function(value: string | null) {
    setSelectedCategory(value || '');
  }, []);

  const handleSortMethodChange = useCallback(function(value: 'name:asc' | 'name:desc' | null) {
    setSortMethod((value as 'name:asc' | 'name:desc') || 'name:asc');
  }, []);

  const {
    massComms: {
      selectRecipients,
      SET_SELECTED_RECIPIENT_TYPE,
      SET_IMPACT_RESPONSE_ITEMS,
      addGroup,
      removeGroup,
      populateImpactedPeopleToSelectedRecipients,
      loadNextPagePersons,
      loadNextPagePeopleGroups
    }
  } = useDispatch<Dispatch>();

  // Set default recipient type based on navigation source
  useEffect(() => {
    if (!massCommsNavigation.fromArc && selectedRecipientType !== 'singleGroupSelect') {
      SET_SELECTED_RECIPIENT_TYPE('singleGroupSelect' as RecipientMainSelection);
    }
  }, [massCommsNavigation.fromArc, selectedRecipientType, SET_SELECTED_RECIPIENT_TYPE]);

  // Lazy loading scroll handlers
  const handlePeopleScroll = useCallback(() => {
    loadNextPagePersons({});
  }, [loadNextPagePersons]);

  const handleGroupsScroll = useCallback(() => {
    loadNextPagePeopleGroups({});
  }, [loadNextPagePeopleGroups]);

  // Debounced API call for people search
  const debouncedLoadNextPagePersons = useDebouncedCallback(
    (searchCriteria: string) => {
      if (searchCriteria.length >= 3 || searchCriteria.length === 0) {
        loadNextPagePersons({ searchCriteria, pageToLoad: 0 });
      }
    },
    DEBOUNCE_DELAY
  );

  // Debounced API call for group search
  const debouncedLoadNextPagePeopleGroups = useDebouncedCallback(
    (searchCriteria: string, sortCriteria: 'name:asc' | 'name:desc', categoryFilter: string) => {
      if (searchCriteria.length >= 3 || searchCriteria.length === 0) {
        loadNextPagePeopleGroups({
          searchCriteria,
          sortCriteria,
          categoryFilter,
          pageToLoad: 0
        });
      }
    },
    DEBOUNCE_DELAY
  );

  const handleRecipientGroup = useCallback(
    (value: string) => {
      SET_SELECTED_RECIPIENT_TYPE(value as RecipientMainSelection);
    },
    [ SET_SELECTED_RECIPIENT_TYPE ]
  );

  const handlePeopleSelect = useCallback((selectedPerson: PeopleEntity) => {
    selectRecipients(selectedPerson);
  }, [selectRecipients]);

  const handleImpactItemSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const impactItems = { ...impactResponseItems };
    if (event.target.name === 'allImpacted') {
      Object.keys(impactItems).forEach(itemKey => {
        if (impactItems[itemKey as ImpactResponseName].isChecked !== event.target.checked) {
          impactItems[itemKey as ImpactResponseName].isChecked = event.target.checked;
        }
      });
    } else {
      impactItems[event.target.name as ImpactResponseName].isChecked = event.target.checked;
      const isAllOtherSelected = Object.keys(impactItems).filter(item => item !== 'allImpacted').every(impactItem => impactItems[impactItem as ImpactResponseName].isChecked === true);
      impactItems['allImpacted'].isChecked = isAllOtherSelected;
    }
    SET_IMPACT_RESPONSE_ITEMS(impactItems);

    populateImpactedPeopleToSelectedRecipients();
  }, [ SET_IMPACT_RESPONSE_ITEMS, impactResponseItems, populateImpactedPeopleToSelectedRecipients ]);

  const handleAddGroup = useCallback((selectedGroup: GroupEntity) => {
    addGroup(selectedGroup);
  }, [addGroup]);

  const handleRemoveGroup = useCallback((selectedGroup: GroupEntity) => {
    removeGroup(selectedGroup);
  }, [removeGroup]);

  const handleTabChange = useCallback((value: string) => {
    setSelectedTab(value);
  }, []);

  // Focus/blur handlers for search inputs
  const handlePersonSearchFocus = useCallback(() => {
    setIsPersonSearchFocused(true);
  }, []);

  const handlePersonSearchBlur = useCallback(() => {
    setIsPersonSearchFocused(false);
  }, []);

  const handleGroupSearchFocus = useCallback(() => {
    setIsGroupSearchFocused(true);
  }, []);

  const handleGroupSearchBlur = useCallback(() => {
    setIsGroupSearchFocused(false);
  }, []);

  // Search button handlers for immediate search
  const handlePersonSearchButtonClick = useCallback(() => {
    loadNextPagePersons({ searchCriteria: personSearchCriteria, pageToLoad: 0 });
  }, [loadNextPagePersons, personSearchCriteria]);

  const handleGroupSearchButtonClick = useCallback(() => {
    loadNextPagePeopleGroups({
      searchCriteria: groupSearchCriteria,
      sortCriteria: sortMethod,
      categoryFilter: selectedCategory,
      pageToLoad: 0
    });
  }, [loadNextPagePeopleGroups, groupSearchCriteria, sortMethod, selectedCategory]);

  useEffect(() => {
    if (selectedTab !== 'groups') {
      return;
    }

    debouncedLoadNextPagePeopleGroups(groupSearchCriteria, sortMethod, selectedCategory);
  }, [groupSearchCriteria, selectedCategory, sortMethod, debouncedLoadNextPagePeopleGroups, selectedTab]);

  useEffect(() => {
    if (selectedTab !== 'individual') {
      return;
    }

    debouncedLoadNextPagePersons(personSearchCriteria);
  }, [personSearchCriteria, debouncedLoadNextPagePersons, selectedTab]);

  // Merge selection state into people list
  const peopleWithSelection = useMemo(() => {
    const selectedIds = new Set(selectedRecipients.map(p => p.profileId));
    return searchedDeliveryEntities.people.map(person => ({
      ...person,
      isSelected: selectedIds.has(person.profileId)
    }));
  }, [searchedDeliveryEntities.people, selectedRecipients]);

  // Merge selection state into groups list
  const groupsWithSelection = useMemo(() => {
    const selectedGroupIds = new Set(selectedGroups.map(g => g._id));
    return searchedDeliveryEntities.groups.map(group => ({
      ...group,
      isSelected: selectedGroupIds.has(group._id)
    }));
  }, [searchedDeliveryEntities.groups, selectedGroups]);

  return (
    <SelectRecipientsPageComponent
      onRecipientGroupSelected={ handleRecipientGroup }
      selectedRecipient={ selectedRecipientType }
      isDeliverySearchLoading={ isDeliverySearchLoading }
      isLoadingPersons={ isLoadingPersons }
      isLoadingPeopleGroups={ isLoadingPeopleGroups }
      searchedDeliveryEntities={ {
        ...searchedDeliveryEntities,
        people: peopleWithSelection,
        groups: groupsWithSelection
      } }
      onPeopleSelect={ handlePeopleSelect }
      onImpactItemSelect={ handleImpactItemSelect }
      impactResponseItems={ impactResponseItems }
      onAddGroup={ handleAddGroup }
      onRemoveGroup={ handleRemoveGroup }
      selectedGroups={ selectedGroups }
      isNavigatedFromArc={ massCommsNavigation.fromArc }
      selectedTab={ selectedTab }
      onTabChange={ handleTabChange }
      // Lazy loading props
      onPeopleScroll={ handlePeopleScroll }
      onGroupsScroll={ handleGroupsScroll }
      categoryList={ categoryList }
      selectedCategory={ selectedCategory }
      onCategoryChange={ handleCategoryChange }
      sortMethod={ sortMethod }
      onSortMethodChange={ handleSortMethodChange }
      personSearchCriteria={ personSearchCriteria }
      setPersonSearchCriteria={ setPersonSearchCriteria }
      groupSearchCriteria={ groupSearchCriteria }
      setGroupSearchCriteria={ setGroupSearchCriteria }
      fetchInProgressGroupsList={ fetchInProgressGroupsList }
      // Focus state props
      isPersonSearchFocused={ isPersonSearchFocused }
      isGroupSearchFocused={ isGroupSearchFocused }
      onPersonSearchFocus={ handlePersonSearchFocus }
      onPersonSearchBlur={ handlePersonSearchBlur }
      onGroupSearchFocus={ handleGroupSearchFocus }
      onGroupSearchBlur={ handleGroupSearchBlur }
      // Search button props
      onPersonSearchButtonClick={ handlePersonSearchButtonClick }
      onGroupSearchButtonClick={ handleGroupSearchButtonClick }
    />
  );
};

export default SelectRecipientsPage;
