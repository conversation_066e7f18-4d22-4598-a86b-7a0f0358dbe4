export type FieldType = 'text' | 'number' | 'date' | 'boolean' | 'select';

export type ConditionType = 'contains' | 'equals' | 'is_any_of' | 'between' | 'greater_than' | 'less_than' | 'is_true' | 'is_false';

export const filterConfig = {
  fields: [
    {
      value: 'Person Type',
      label: 'Person Type',
      type: 'select' as FieldType,
      data: ['Full-Time Employee', 'Part-Time Employee', 'Contractor', 'Intern', 'Visitor'],
      conditions: ['contains', 'equals', 'is_any_of']
    },
    {
      value: 'Job Title',
      label: 'Job Title',
      type: 'select' as FieldType,
      data: ['VIP', 'Executive', 'CEO', 'Manager', 'Developer', 'Designer', 'Analyst'],
      conditions: ['contains', 'equals', 'is_any_of']
    },
    {
      value: 'Company',
      label: 'Company',
      type: 'select' as FieldType,
      data: ['Tech Corp', 'Startup Inc', 'Global Ltd', 'Innovation Co'],
      conditions: ['contains', 'equals', 'is_any_of']
    },
    {
      value: 'Location',
      label: 'Location',
      type: 'select' as FieldType,
      data: ['London', 'New York', 'San Francisco', 'Berlin', 'Tokyo', 'Sydney'],
      conditions: ['contains', 'equals', 'is_any_of']
    },
    {
      value: 'Tags',
      label: 'Tags',
      type: 'select' as FieldType,
      data: ['VIP', 'Executive', 'Remote', 'On-site', 'International', 'Local'],
      conditions: ['contains', 'equals', 'is_any_of']
    },
    {
      value: 'Travel Status',
      label: 'Travel Status',
      type: 'select' as FieldType,
      data: ['Traveling', 'Not Traveling', 'Planning Travel', 'Returned'],
      conditions: ['contains', 'equals', 'is_any_of']
    },
    {
      value: 'VIP Status',
      label: 'VIP Status',
      type: 'boolean' as FieldType,
      data: ['Is True', 'Is False'],
      conditions: ['is_true', 'is_false']
    },
    {
      value: 'Start Date',
      label: 'Start Date',
      type: 'date' as FieldType,
      data: [],
      conditions: ['equals', 'between', 'greater_than', 'less_than']
    },
    {
      value: 'Age',
      label: 'Age',
      type: 'number' as FieldType,
      data: [],
      conditions: ['equals', 'between', 'greater_than', 'less_than']
    },
    {
      value: 'Status',
      label: 'Status',
      type: 'select' as FieldType,
      data: ['Active', 'Inactive', 'Pending', 'Suspended'],
      conditions: ['contains', 'equals', 'is_any_of']
    }
  ],
  
  conditionLabels: {
    contains: 'Contains',
    equals: 'Equals',
    is_any_of: 'Is any of',
    between: 'Between',
    greater_than: 'Greater than',
    less_than: 'Less than',
    is_true: 'Is True',
    is_false: 'Is False'
  }
};

export const getFieldData = (fieldName: string): string[] => {
  const field = filterConfig.fields.find(f => f.value === fieldName);
  return field?.data || [];
};

export const getFieldOptions = () => {
  return filterConfig.fields.map(field => ({
    value: field.value,
    label: field.label
  }));
};

export const getFieldType = (fieldName: string): FieldType => {
  const field = filterConfig.fields.find(f => f.value === fieldName);
  return field?.type || 'text';
};

export const getConditionOptions = (fieldName: string) => {
  const field = filterConfig.fields.find(f => f.value === fieldName);
  if (!field) {
    return [];
  }
  
  return field.conditions.map(condition => ({
    value: condition,
    label: filterConfig.conditionLabels[condition as keyof typeof filterConfig.conditionLabels]
  }));
};

export const getValueFieldType = (fieldName: string, conditionType: string): 'text' | 'number' | 'date' | 'boolean' | 'select' | 'date_range' | 'number_range' => {
  const fieldType = getFieldType(fieldName);
  
  if (conditionType === 'between') {
    if (fieldType === 'date') {
      return 'date_range';
    }
    if (fieldType === 'number') {
      return 'number_range';
    }
  }
  
  if (conditionType === 'is_true' || conditionType === 'is_false') {
    return 'boolean';
  }
  
  return fieldType;
}; 