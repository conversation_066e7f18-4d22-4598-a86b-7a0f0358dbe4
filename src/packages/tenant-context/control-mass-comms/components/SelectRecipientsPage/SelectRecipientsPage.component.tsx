import { 
  Box, 
  Button,  Checkbox, 
  Collapse, 
  Flex, 
  Radio, 
  ScrollArea,
  SegmentedControl, 
  Select,
  Stack,
  Text, 
  TextInput } from '@mantine/core';
import { FC, useCallback, useRef } from 'react';
import { useDebouncedCallback } from 'use-debounce';

import Spinner from '@/common/components/Spinner';
import { ReactComponent as SearchIcon } from '@/common/icons/search.svg';
import LocalErrorBoundary from '@/tenant-context/common/components/LocalErrorBoundary';
import RecipientsListView from '@/tenant-context/control-mass-comms/components/SelectRecipientsPage/RecipientsListView';
import {
  GroupEntity,
  ImpactedPeopleSet,
  PeopleEntity,
  RecipientMainSelection,
  SearchedDeliveryEntities
} from '@/tenant-context/control-mass-comms/types/MassComms.types';

import GroupListItem from './GroupListItem.component';
import GroupListItemSkeleton from './GroupListItemSkeleton';
import PersonListItem from './PersonListItem.component';
import PersonListItemSkeleton from './PersonListItemSkeleton';
import { useArcSegmentedControlStyles,useSelectRecipientsPageStyles } from './SelectRecipientsPage.styles';

const SCROLL_AREA_HEIGHT = 840;

type Props = {
  onRecipientGroupSelected: (value: string) => void,
  selectedRecipient: RecipientMainSelection,
  onPeopleSelect: (item: PeopleEntity) => void,
  isDeliverySearchLoading: boolean | undefined,
  isLoadingPersons: boolean,
  isLoadingPeopleGroups: boolean,
  searchedDeliveryEntities: SearchedDeliveryEntities
  onImpactItemSelect: (event: React.ChangeEvent<HTMLInputElement>) => void
  impactResponseItems: ImpactedPeopleSet
  onAddGroup: (item: GroupEntity) => void,
  onRemoveGroup: (item: GroupEntity) => void,
  selectedGroups: Array<GroupEntity>,
  isNavigatedFromArc: boolean,
  selectedTab: string,
  onTabChange: (value: string) => void,
  // Lazy loading props
  onPeopleScroll: () => void,
  onGroupsScroll: () => void,
  categoryList: string[],
  selectedCategory: string,
  onCategoryChange: (value: string | null) => void,
  sortMethod: 'name:asc' | 'name:desc',
  onSortMethodChange: (value: 'name:asc' | 'name:desc' | null) => void,
  personSearchCriteria: string,
  setPersonSearchCriteria: (value: string) => void,
  groupSearchCriteria: string,
  setGroupSearchCriteria: (value: string) => void,
  fetchInProgressGroupsList: string[],
  // Focus state props
  isPersonSearchFocused: boolean,
  isGroupSearchFocused: boolean,
  onPersonSearchFocus: () => void,
  onPersonSearchBlur: () => void,
  onGroupSearchFocus: () => void,
  onGroupSearchBlur: () => void,
  // Search button props
  onPersonSearchButtonClick: () => void,
  onGroupSearchButtonClick: () => void,
}

const SelectRecipientsPageComponent: FC<Props> = ({
  onRecipientGroupSelected,
  selectedRecipient,
  isDeliverySearchLoading,
  isLoadingPersons,
  isLoadingPeopleGroups,
  searchedDeliveryEntities,
  onPeopleSelect,
  onAddGroup,
  onRemoveGroup,
  onImpactItemSelect,
  impactResponseItems,
  isNavigatedFromArc,
  selectedTab,
  onTabChange,
  // Lazy loading props
  onPeopleScroll,
  onGroupsScroll,
  categoryList,
  selectedCategory,
  onCategoryChange,
  sortMethod,
  onSortMethodChange,
  personSearchCriteria,
  setPersonSearchCriteria,
  groupSearchCriteria,
  setGroupSearchCriteria,
  fetchInProgressGroupsList,
  // Focus state props
  isPersonSearchFocused,
  isGroupSearchFocused,
  onPersonSearchFocus,
  onPersonSearchBlur,
  onGroupSearchFocus,
  onGroupSearchBlur,
  // Search button props
  onPersonSearchButtonClick,
  onGroupSearchButtonClick
}) => {

  const { classes, cx } = useSelectRecipientsPageStyles();
  const { classes: arcSegmentedControlClasses } = useArcSegmentedControlStyles();

  const personsViewportRef = useRef<HTMLDivElement>(null);
  const groupsViewportRef = useRef<HTMLDivElement>(null);

  // Filter and sort groups for display
  let filteredGroups = searchedDeliveryEntities.groups;
  if (selectedCategory) {
    filteredGroups = filteredGroups.filter(function(group) {
      return group.category === selectedCategory;
    });
  }
  filteredGroups = filteredGroups.sort(function(a, b) {
    if (sortMethod === 'name:asc') {
      return a.name.localeCompare(b.name);
    } else {
      return b.name.localeCompare(a.name);
    }
  });

  const onPersonSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPersonSearchCriteria(event.target.value);
  };

  const onGroupSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setGroupSearchCriteria(event.target.value);
  };

  const debouncedOnPeopleScroll = useDebouncedCallback(onPeopleScroll, 300);
  const debouncedOnGroupsScroll = useDebouncedCallback(onGroupsScroll, 300);

  const onPersonListScrollPositionChange = useCallback((_position: { x: number, y: number }) => {
    const scrollTop = personsViewportRef.current?.scrollTop || 0;
    const scrollHeight = personsViewportRef.current?.scrollHeight || 0;
    const clientHeight = personsViewportRef.current?.clientHeight || 0;

    if ((scrollTop + clientHeight) >= (scrollHeight - 20) && !isLoadingPersons) {
      debouncedOnPeopleScroll();
    }
  }, [debouncedOnPeopleScroll, isLoadingPersons]);

  const onGroupListScrollPositionChange = useCallback((_position: { x: number, y: number }) => {
    const scrollTop = groupsViewportRef.current?.scrollTop || 0;
    const scrollHeight = groupsViewportRef.current?.scrollHeight || 0;
    const clientHeight = groupsViewportRef.current?.clientHeight || 0;

    if ((scrollTop + clientHeight) >= (scrollHeight - 20) && !isLoadingPeopleGroups) {
      debouncedOnGroupsScroll();
    }
  }, [debouncedOnGroupsScroll, isLoadingPeopleGroups]);

  return (
    <Box className={ classes.recipientContainer }>
      <LocalErrorBoundary>
        <Box className={ classes.cardSection }>
          <Text className={ classes.sectionHeader }>Add Recipients</Text>
          
          { isNavigatedFromArc && (
            <Radio.Group
              orientation='vertical'
              onChange={ onRecipientGroupSelected }
              value={ selectedRecipient }
              className={ classes.radioGroup }
            >
              <Flex gap='md' p={ 4 }>
                <Box>
                  <Radio value='impactSelect' label='Select People Impacted'/>
                </Box>
                <Box>
                  <Radio value='singleGroupSelect' label='Select Individual or Groups' />
                </Box>
              </Flex>
            </Radio.Group>
          ) }
              
          <Collapse in={ selectedRecipient === 'impactSelect' }>
            <Box p='lg' className={ classes.checkItemsContainer }>
              <Stack spacing='sm'>
                <Flex align='center' justify='space-between' gap='sm'>
                  <Flex align='center' gap='sm'>
                    <Checkbox
                      size='sm'
                      name='allImpacted'
                      onChange={ onImpactItemSelect }
                      checked={ impactResponseItems.allImpacted.isChecked }
                    />
                    <Text size='sm'>
                      All People Impacted
                      ({ impactResponseItems.notSafe.people.length + impactResponseItems.noResponse.people.length
                      + impactResponseItems.okPeople.people.length + impactResponseItems.otherPeople.people.length })
                    </Text>
                  </Flex>
                </Flex>
                
                <Flex align='center' justify='space-between' gap='sm'>
                  <Flex align='center' gap='sm'>
                    <Checkbox
                      size='sm'
                      name='notSafe'
                      onChange={ onImpactItemSelect }
                      checked={ impactResponseItems.notSafe.isChecked }
                    />
                    <Text size='sm'>
                      Not Safe People ({ impactResponseItems.notSafe.people.length })
                    </Text>
                  </Flex>
                  <Box className={ classes.checkItemCircle } sx={ { backgroundColor: '#DC2020' } }></Box>
                </Flex>
                
                <Flex align='center' justify='space-between' gap='sm'>
                  <Flex align='center' gap='sm'>
                    <Checkbox
                      size='sm'
                      name='noResponse'
                      onChange={ onImpactItemSelect }
                      checked={ impactResponseItems.noResponse.isChecked }
                    />
                    <Text size='sm'>
                      No Response People ({ impactResponseItems.noResponse.people.length })
                    </Text>
                  </Flex>
                  <Box className={ classes.checkItemCircle } sx={ { backgroundColor: '#FFC94A' } }></Box>
                </Flex>
                
                <Flex align='center' justify='space-between' gap='sm'>
                  <Flex align='center' gap='sm'>
                    <Checkbox
                      size='sm'
                      name='okPeople'
                      onChange={ onImpactItemSelect }
                      checked={ impactResponseItems.okPeople.isChecked }
                    />
                    <Text size='sm'>
                      OK People ({ impactResponseItems.okPeople.people.length })
                    </Text>
                  </Flex>
                  <Box className={ classes.checkItemCircle } sx={ { backgroundColor: '#47815D' } }></Box>
                </Flex>
                
                <Flex align='center' justify='space-between' gap='sm'>
                  <Flex align='center' gap='sm'>
                    <Checkbox
                      size='sm'
                      name='otherPeople'
                      onChange={ onImpactItemSelect }
                      checked={ impactResponseItems.otherPeople.isChecked }
                    />
                    <Text size='sm'>
                      Other People ({ impactResponseItems.otherPeople.people.length })
                    </Text>
                  </Flex>
                  <Box className={ classes.checkItemCircle } sx={ { backgroundColor: '#D9D9D9' } }></Box>
                </Flex>
              </Stack>
            </Box>
          </Collapse>
          
          <Collapse in={ selectedRecipient === 'singleGroupSelect' }>
            <Flex direction='column' gap='md'>
              <SegmentedControl
                value={ selectedTab }
                onChange={ onTabChange }
                data={ [
                  { label: 'Individual Persons', value: 'individual' },
                  { label: 'People Groups', value: 'groups' }
                ] }
                size='lg'
                classNames={ arcSegmentedControlClasses }
              />
              
              { selectedTab === 'individual' && (
                <Box>
                  <TextInput
                    name='people'
                    className={ cx(classes.searchTextInput, {
                      [classes.searchTextInputLoading]: personSearchCriteria && isLoadingPersons,
                      [classes.searchTextInput]: !personSearchCriteria || !isLoadingPersons
                    }) }
                    placeholder={ isPersonSearchFocused ? 'Min 3 characters...' : 'Search by name, email, and attribute...' }
                    icon={ <SearchIcon /> }
                    rightSection={ <Flex align="center" gap="xs">
                      { (personSearchCriteria && isLoadingPersons) && <Spinner size={ 20 } /> }
                      <Button
                        variant="filled"
                        onClick={ onPersonSearchButtonClick }
                        size="xs"
                        disabled={ isLoadingPersons }
                      >
                        Search
                      </Button>
                    </Flex> }
                    rightSectionWidth={ 70 }
                    value={ personSearchCriteria }
                    onChange={ onPersonSearchChange }
                    onFocus={ onPersonSearchFocus }
                    onBlur={ onPersonSearchBlur }
                    mb="md"
                  />

                  <ScrollArea
                    h={ SCROLL_AREA_HEIGHT }
                    className={ classes.scrollArea }
                    onScrollPositionChange={ onPersonListScrollPositionChange }
                    viewportRef={ personsViewportRef }
                  >
                    <Stack spacing={ 0 }>
                      { isDeliverySearchLoading &&
                        [...Array(10)].map((_, idx) => (
                          <PersonListItemSkeleton key={ idx } />
                        )) }
                      { !isLoadingPersons && searchedDeliveryEntities.people.length === 0 && (
                        <Box p='md' ta='center'>
                          <Text size='sm' color='dimmed'>No recipients found!</Text>
                        </Box>
                      ) }
                      { !isDeliverySearchLoading && searchedDeliveryEntities.people.length > 0 &&
                        searchedDeliveryEntities.people
                          .map((person: PeopleEntity) => (
                            <PersonListItem
                              key={ person.profileId }
                              person={ person }
                              onPeopleSelect={ onPeopleSelect }
                            />
                          )) }
                      { !isDeliverySearchLoading && isLoadingPersons &&
                        [...Array(5)].map((_, idx) => (
                          <PersonListItemSkeleton key={ `lazy-person-skeleton-${idx}` } />
                        )) }
                    </Stack>
                  </ScrollArea>
                </Box>
              ) }

              { selectedTab === 'groups' && (
                <Box>
                  <TextInput
                    name='selectedGroups'
                    className={ cx(classes.searchTextInput, {
                      [classes.searchTextInputLoading]: groupSearchCriteria && isLoadingPeopleGroups,
                      [classes.searchTextInput]: !groupSearchCriteria || !isLoadingPeopleGroups
                    }) }
                    placeholder={ isGroupSearchFocused ? 'Min 3 characters...' : 'Start typing a people group name' }
                    icon={ <SearchIcon /> }
                    value={ groupSearchCriteria }
                    onChange={ onGroupSearchChange }
                    onFocus={ onGroupSearchFocus }
                    onBlur={ onGroupSearchBlur }
                    rightSection={ <Flex align="center" gap="xs" justify="flex-end">
                      { (groupSearchCriteria && isLoadingPeopleGroups) && <Spinner size={ 20 } /> }
                      <Button
                        variant="filled"
                        onClick={ onGroupSearchButtonClick }
                        size="xs"
                        disabled={ isLoadingPeopleGroups }
                      >
                        Search
                      </Button>
                    </Flex> }
                    rightSectionWidth={ 70 }
                    mb='md'
                  />
                  <Flex className={ classes.groupFiltersRow }>
                    <Select
                      className={ classes.groupFilterSelect }
                      placeholder="Category"
                      data={ categoryList.map(function(cat) {
                        return { value: cat, label: cat }; 
                      }) }
                      value={ selectedCategory }
                      onChange={ onCategoryChange }
                      disabled={ categoryList.length === 0 }
                      clearable
                      mb='0'
                    />
                    <Select
                      className={ classes.groupFilterSelect }
                      placeholder="Show A-Z"
                      data={ [
                        { value: 'name:asc', label: 'Show A-Z' },
                        { value: 'name:desc', label: 'Show Z-A' }
                      ] }
                      value={ sortMethod }
                      onChange={ onSortMethodChange }
                      mb='0'
                    />
                  </Flex>
                  
                  <ScrollArea
                    h={ SCROLL_AREA_HEIGHT }
                    className={ classes.scrollArea }
                    onScrollPositionChange={ onGroupListScrollPositionChange }
                    viewportRef={ groupsViewportRef }
                  >
                    <Stack spacing={ 0 }>
                      { isDeliverySearchLoading &&
                        [...Array(10)].map((_, idx) => <GroupListItemSkeleton key={ idx } />) }
                      { !isLoadingPeopleGroups && filteredGroups.length === 0 && (
                        <Box p='md' ta='center'>
                          <Text size='sm' color='dimmed'>No groups found!</Text>
                        </Box>
                      ) }
                      { !isDeliverySearchLoading && filteredGroups.length > 0 &&
                        filteredGroups
                          .map(searchedGroup => (
                            <GroupListItem
                              key={ searchedGroup._id }
                              group={ searchedGroup }
                              onAddGroup={ onAddGroup }
                              onRemoveGroup={ onRemoveGroup }
                              loading={ fetchInProgressGroupsList.includes(searchedGroup._id) }
                            />
                          )) }
                      { !isDeliverySearchLoading && isLoadingPeopleGroups &&
                        [...Array(5)].map((_, idx) => <GroupListItemSkeleton key={ `lazy-group-skeleton-${idx}` } />) }
                    </Stack>
                  </ScrollArea>
                </Box>
              ) }
            </Flex>
          </Collapse>
        </Box>
      </LocalErrorBoundary>
      
      <LocalErrorBoundary>
        <RecipientsListView />
      </LocalErrorBoundary>
    </Box>
  );
};

export default SelectRecipientsPageComponent;
