import { 
  Badge, 
  Box, 
  Button, 
  Flex, 
  MultiSelect, 
  Select, 
  Stack, 
  Text, 
  TextInput, 
  Title } from '@mantine/core';
import { FC } from 'react';

import { ReactComponent as IconX } from '@/common/icons/Actions/close-icon.svg';
import { Card } from '@/tenant-context/common/components/Page';

import { useCreateDynamicGroup } from './CreateDynamicGroup.context';
import { useCreateDynamicGroupStyles } from './CreateDynamicGroup.styles';

const EditFiltersPanel: FC = () => {
  const { classes } = useCreateDynamicGroupStyles();
  const {
    selectedFilter,
    editFilterField,
    editFilterOperator,
    editFilterValues,
    availableValues,
    conditionOptions,
    valueFieldType,
    setEditFilterField,
    setEditFilterOperator,
    setEditFilterValues,
    handleConfirmFilter,
    handleCancelFilter
  } = useCreateDynamicGroup();

  const renderValueField = () => {
    switch (valueFieldType) {
    case 'select':
      return (
        <MultiSelect
          label="Add Values *"
          value={ editFilterValues }
          onChange={ setEditFilterValues }
          data={ availableValues }
          styles={ {
            label: { color: '#FFFFFF' },
            input: { 
              backgroundColor: '#1E3445', 
              border: '1px solid #576275',
              color: '#FFFFFF'
            }
          } }
          valueComponent={ ({ value, onRemove }) => (
            <Badge
              key={ value }
              variant="filled"
              className={ classes.categoryBadge }
              rightSection={ <IconX
                onClick={ () => onRemove(value) }
                style={ { cursor: 'pointer' } }
              /> }
            >
              { value }
            </Badge>
          ) }
        />
      );
      
    case 'boolean':
      return (
        <Select
          label="Value *"
          value={ editFilterValues[0] || '' }
          onChange={ (value) => setEditFilterValues(value ? [value] : []) }
          data={ ['Is True', 'Is False'] }
          styles={ {
            label: { color: '#FFFFFF' },
            input: { 
              backgroundColor: '#1E3445', 
              border: '1px solid #576275',
              color: '#FFFFFF'
            }
          } }
        />
      );
      
    case 'date':
      return (
        <TextInput
          label="Value *"
          type="date"
          value={ editFilterValues[0] || '' }
          onChange={ (e) => setEditFilterValues([e.target.value]) }
          styles={ {
            label: { color: '#FFFFFF' },
            input: { 
              backgroundColor: '#1E3445', 
              border: '1px solid #576275',
              color: '#FFFFFF'
            }
          } }
        />
      );
      
    case 'number':
      return (
        <TextInput
          label="Value *"
          type="number"
          value={ editFilterValues[0] || '' }
          onChange={ (e) => setEditFilterValues([e.target.value]) }
          styles={ {
            label: { color: '#FFFFFF' },
            input: { 
              backgroundColor: '#1E3445', 
              border: '1px solid #576275',
              color: '#FFFFFF'
            }
          } }
        />
      );
      
    case 'date_range':
      return (
        <Flex gap="sm">
          <TextInput
            label="From Date *"
            type="date"
            value={ editFilterValues[0] || '' }
            onChange={ (e) => setEditFilterValues([e.target.value, editFilterValues[1] || '']) }
            styles={ {
              label: { color: '#FFFFFF' },
              input: { 
                backgroundColor: '#1E3445', 
                border: '1px solid #576275',
                color: '#FFFFFF'
              }
            } }
          />
          <TextInput
            label="To Date *"
            type="date"
            value={ editFilterValues[1] || '' }
            onChange={ (e) => setEditFilterValues([editFilterValues[0] || '', e.target.value]) }
            styles={ {
              label: { color: '#FFFFFF' },
              input: { 
                backgroundColor: '#1E3445', 
                border: '1px solid #576275',
                color: '#FFFFFF'
              }
            } }
          />
        </Flex>
      );
      
    case 'number_range':
      return (
        <Flex gap="sm">
          <TextInput
            label="From *"
            type="number"
            value={ editFilterValues[0] || '' }
            onChange={ (e) => setEditFilterValues([e.target.value, editFilterValues[1] || '']) }
            styles={ {
              label: { color: '#FFFFFF' },
              input: { 
                backgroundColor: '#1E3445', 
                border: '1px solid #576275',
                color: '#FFFFFF'
              }
            } }
          />
          <TextInput
            label="To *"
            type="number"
            value={ editFilterValues[1] || '' }
            onChange={ (e) => setEditFilterValues([editFilterValues[0] || '', e.target.value]) }
            styles={ {
              label: { color: '#FFFFFF' },
              input: { 
                backgroundColor: '#1E3445', 
                border: '1px solid #576275',
                color: '#FFFFFF'
              }
            } }
          />
        </Flex>
      );
      
    default:
      return (
        <TextInput
          label="Value *"
          value={ editFilterValues[0] || '' }
          onChange={ (e) => setEditFilterValues([e.target.value]) }
          styles={ {
            label: { color: '#FFFFFF' },
            input: { 
              backgroundColor: '#1E3445', 
              border: '1px solid #576275',
              color: '#FFFFFF'
            }
          } }
        />
      );
    }
  };

  return (
    <Card className={ classes.rightPanel }>
      <Title order={ 3 } mb="8px" style={ { color: '#FFFFFF' } }>Edit Filters</Title>
      <Text size="sm" style={ { color: '#576275', marginBottom: '24px' } }>
        xxx supporting copy
      </Text>

      { selectedFilter ? (
        <Stack spacing={ 16 } p='xl' className={ classes.filterConfigContainer }>
          <Select
            label="Add Filter"
            required
            value={ editFilterField }
            onChange={ (value) => setEditFilterField(value || '') }
            data={ conditionOptions }
            styles={ {
              label: { color: '#FFFFFF' },
              input: { 
                backgroundColor: '#1E3445', 
                border: '1px solid #576275',
                color: '#FFFFFF'
              }
            } }
          />
          
          <Select
            label="Conditions *"
            value={ editFilterOperator }
            onChange={ (value) => setEditFilterOperator(value || '') }
            data={ conditionOptions }
            styles={ {
              label: { color: '#FFFFFF' },
              input: { 
                backgroundColor: '#1E3445', 
                border: '1px solid #576275',
                color: '#FFFFFF'
              }
            } }
          />
          
          { renderValueField() }

          <Flex justify="flex-start" gap='sm' mt="16px">
            <Button 
              variant="outline" 
              className={ classes.filterButton }
              onClick={ handleCancelFilter }
            >
              Cancel
            </Button>
            <Button 
              className={ classes.confirmButton }
              onClick={ handleConfirmFilter }
            >
              Confirm
            </Button>
          </Flex>
        </Stack>
      ) : (
        <Box className={ classes.editPlaceholder }>
          <Text>Select a filter to start editing</Text>
        </Box>
      ) }
    </Card>
  );
};

export default EditFiltersPanel; 