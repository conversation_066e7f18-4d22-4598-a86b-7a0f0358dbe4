import { 
  ActionIcon, 
  Badge, 
  Box, 
  Divider, 
  Flex, 
  Group, 
  MultiSelect, 
  Stack, 
  Text, 
  TextInput, 
  Title,
  UnstyledButton } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { FC, Fragment } from 'react';

import { ReactComponent as IconX } from '@/common/icons/Actions/close-icon.svg';
import { ReactComponent as IconCopy } from '@/common/icons/Actions/copy-icon.svg';
import { ReactComponent as IconTrash } from '@/common/icons/Actions/delete-icon.svg';
import { Card } from '@/tenant-context/common/components/Page';

import { useCreateDynamicGroup } from './CreateDynamicGroup.context';
import { useCreateDynamicGroupStyles } from './CreateDynamicGroup.styles';

const GroupSettingsPanel: FC = () => {
  const { classes } = useCreateDynamicGroupStyles();
  const {
    groupName,
    categories,
    conditionGroups,
    setGroupName,
    setCategories,
    handleAddConditionGroup,
    handleAddCondition,
    handleDeleteCondition,
    handleDeleteGroup,
    handleCopyGroup,
    handleEditFilter
  } = useCreateDynamicGroup();

  const renderConditionText = (condition: any) => {
    const { field, operator, values } = condition;
    
    const highlightedField = (
      <span className={ classes.highlightedText }>
        { field }
      </span>
    );

    // Format values with "or" between them for better readability
    const formatValues = (valuesInstances: string[]) => {
      if (valuesInstances.length === 0) {
        return '';
      }
      if (valuesInstances.length === 1) {
        return valuesInstances[0];
      } 
      
      return valuesInstances.map((value, index) => {
        const highlightedValue = (
          <span key={ index } className={ classes.highlightedText }>
            { value }
          </span>
        );
        
        if (index === 0) {
          return highlightedValue;
        }
        if (index === valuesInstances.length - 1) {
          return <span key={ index }> or { highlightedValue }</span>;
        }

        return <span key={ index }>, { highlightedValue }</span>;
      });
    };
    
    switch (operator) {
    case 'contains':
      return <>{ highlightedField } contains { formatValues(values) }</>;
    case 'between':
      return <>{ highlightedField } between { formatValues(values) }</>;
    case 'is any of':
      return <>{ highlightedField } is any of { formatValues(values) }</>;
    case 'equals':
      return <>{ highlightedField } equals { formatValues(values) }</>;
    default:
      return <>{ highlightedField } { operator } { formatValues(values) }</>;
    }
  };

  return (
    <Card className={ classes.leftPanel }>
      <Stack spacing={ 24 }>
        { /* Group Settings */ }
        <Box>
          <Title order={ 3 } className={ classes.sectionTitle }>Group Settings</Title>
          
          <Stack spacing={ 16 }>
            <TextInput
              label="Group Name *"
              value={ groupName }
              onChange={ (e) => setGroupName(e.target.value) }
              styles={ {
                label: { color: '#FFFFFF' },
                input: { 
                  backgroundColor: '#2A2D31', 
                  border: '1px solid #576275',
                  color: '#FFFFFF'
                }
              } }
            />
            
            <MultiSelect
              label="Category"
              data={ ['My Category 1', 'London', 'Engineering', 'Management'] }
              value={ categories }
              onChange={ setCategories }
              valueComponent={ ({ value, onRemove }) => (
                <Badge
                  key={ value }
                  variant="filled"
                  className={ classes.categoryBadge }
                  rightSection={ <IconX
                    onClick={ () => onRemove(value) }
                    style={ { cursor: 'pointer' } }
                  /> }
                >
                  { value }
                </Badge>
              ) }
            />
          </Stack>
        </Box>

        { /* Setup Group Conditions */ }
        <Box>
          <Title order={ 3 } className={ classes.sectionTitle }>Setup Group Conditions</Title>
          
          <Stack spacing={ 16 }  className={ classes.conditionGroupContainer }>
            { conditionGroups.map((group, groupIndex) => (
              <Stack key={ group.id }>
                <Box
                  className={ classes.conditionGroup }
                >
                  <Flex className={ classes.groupHeader }>
                    <Text className={ classes.groupTitle }>
                      Group #{ group.id }
                    </Text>
                    <Group spacing={ 8 }>
                      <ActionIcon 
                        size="sm" 
                        variant="subtle"
                        onClick={ () => handleCopyGroup(group.id) }
                      >
                        <IconCopy/>
                      </ActionIcon>
                      <ActionIcon 
                        size="sm" 
                        variant="subtle"
                        onClick={ () => handleDeleteGroup(group.id) }
                      >
                        <IconTrash/>
                      </ActionIcon>
                    </Group>
                  </Flex>

                  <Stack spacing={ 12 }>
                    { group.conditions.length === 0 ? (
                      <Box className={ classes.emptyFiltersPlaceholder }>
                        <Text size="sm" color="dimmed">
                          Filters will appear here once applied
                        </Text>
                      </Box>
                    ) : (
                      group.conditions.map((condition, conditionIndex) => (
                        <Fragment key={ condition.id }>
                          <Flex justify="space-between" align="center" className={ classes.conditionItem }>
                            <Text 
                              className={ classes.conditionText }
                              onClick={ () => handleEditFilter(condition) }
                            >
                              { condition.field && condition.operator ? renderConditionText(condition) : 'Click to configure filter' }
                            </Text>
                            <ActionIcon 
                              size="sm" 
                              variant="subtle"
                              onClick={ () => handleDeleteCondition(group.id, condition.id) }
                            >
                              <IconTrash/>
                            </ActionIcon>
                          </Flex>
                          
                          { conditionIndex < group.conditions.length - 1 && (
                            <Text weight='bold'>
                              And
                            </Text>
                          ) }
                        </Fragment>
                      ))
                    ) }
                    
                    <Box
                      className={ classes.addFilterBox }
                      onClick={ () => handleAddCondition(group.id) }
                    >
                      <IconPlus size={ 16 } />
                      <Text weight='bold'>
                        Add Filter
                      </Text>
                    </Box>
                  </Stack>
                </Box>
                { groupIndex < conditionGroups.length - 1 && (
                  <Flex justify="center" align="center" gap='8px'>
                    <Divider
                      w={ 40 }
                      labelPosition="center"
                      className={ classes.divider }
                    />
                    <Text weight='bold'>
                      Or
                    </Text>
                    <Divider
                      w={ 40 }
                      labelPosition="center"
                      className={ classes.divider }
                    />
                  </Flex>
                ) }
              </Stack>
            )) }

            <UnstyledButton
              className={ classes.addGroupButton }
              onClick={ handleAddConditionGroup }
            >
              Add another Group
            </UnstyledButton>
          </Stack>
        </Box>
      </Stack>
    </Card>
  );
};

export default GroupSettingsPanel; 