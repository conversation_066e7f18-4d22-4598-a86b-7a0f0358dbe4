/* eslint-disable max-lines */
import { 
  ActionIcon, 
  Button, 
  Flex, 
  Tabs } from '@mantine/core';
import { IconBell } from '@tabler/icons-react';
import { FC } from 'react';

import Page, { Body, Header } from '@/tenant-context/common/components/Page';

import { useCreateDynamicGroup } from './CreateDynamicGroup.context';
import { useCreateDynamicGroupStyles } from './CreateDynamicGroup.styles';
import { Props } from './CreateDynamicGroup.types';
import EditFiltersPanel from './EditFiltersPanel.component';
import GroupSettingsPanel from './GroupSettingsPanel.component';

const CreateDynamicGroupComponent: FC<Props> = ({ title = 'Create Dynamic Group' }) => {
  const { classes } = useCreateDynamicGroupStyles();
  const { activeTab, setActiveTab, handleBackClick } = useCreateDynamicGroup();

  return (
    <Page>
      <Header heading={ title }>
        <ActionIcon size="lg" variant="subtle">
          <IconBell size={ 20 } />
        </ActionIcon>
      </Header>

      <Body p='xl' gap={ 24 }>
        { /* Tabs */ }
        <Tabs value={ activeTab }>
          <Tabs.List>
            <Tabs.Tab value="conditions">
              Conditions
            </Tabs.Tab>
            <Tabs.Tab value="people">
              People In Group (256)
            </Tabs.Tab>
          </Tabs.List>
        </Tabs>

        <Flex className={ classes.mainContent }>
          { /* Left Panel */ }
          <GroupSettingsPanel />

          { /* Right Panel - Edit Filters */ }
          <EditFiltersPanel />
        </Flex>

        { /* Footer */ }
        <Flex className={ classes.footer }>
          <Button 
            variant="outline" 
            className={ classes.cancelButton }
            onClick={ handleBackClick }
          >
            Cancel
          </Button>
          <Button 
            className={ classes.createButton }
          >
            Create Group
          </Button>
        </Flex>
      </Body>
    </Page>
  );
};

export default CreateDynamicGroupComponent;