export type FilterCondition = {
  id: string;
  field: string;
  operator: string;
  values: string[];
};

export type ConditionGroup = {
  id: string;
  conditions: FilterCondition[];
};

export type Props = {
  title?: string;
};

export type FilterField = 
  | 'Person Type'
  | 'Job Title'
  | 'Company'
  | 'Location'
  | 'Tags'
  | 'Travel Status'
  | 'VIP Status'
  | 'Start Date'
  | 'Status';

export type FilterOperator = 
  | 'Contains'
  | 'Equals'
  | 'Is any of'
  | 'Between'
  | 'Greater than'
  | 'Less than'
  | 'contains'
  | 'equals'
  | 'is any of'
  | 'between'
  | 'greater than'
  | 'less than';