import { Flex, ScrollArea } from '@mantine/core';
import { FlexProps } from '@mantine/core/lib/Flex/Flex';
import { FC } from 'react';

import { usePageStyles } from '@/tenant-context/common/components/Page/Page.styles';

type Props = {
  gap?: number;
} & FlexProps

const Page: FC<Props> = ({ children, gap, ...others }) => {

  const { cx, classes } = usePageStyles();

  return (
    <Flex className={ cx(classes.cardRoot, others.className) }>
      <ScrollArea w='100%' p='xl'>
        <Flex
          direction='column'
          p='xl' gap={ gap }
          // eslint-disable-next-line react/jsx-props-no-spreading
          { ...others }
        >
          { children }
        </Flex>
      </ScrollArea>
    </Flex>
  );
};

export default Page;
