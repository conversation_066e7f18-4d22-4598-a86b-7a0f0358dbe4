import { <PERSON><PERSON>, Menu } from "@mantine/core";
import { FC, useCallback, useContext } from 'react';
import { Link, useNavigate } from "react-router-dom";

import usePermission from "@/common/hooks/usePermission";
import { ReactComponent as Arrow } from '@/common/icons/Arrows/right-arrow.svg';
import { RouterConfig } from "@/core/components/Router/Router.config";
import { useControlGroupsStyles } from "@/tenant-context/control-groups/components/ControlGroups/ControlGroups.styles";
import ControlGroupsTable from "@/tenant-context/control-groups/components/ControlGroupsTable";
import { CreateGroupModalContext } from "@/tenant-context/control-groups/components/CreateGroupModal/CreateGroupModal.context";
import { PeopleGroupsPoliciesConfig } from "@/tenant-context/control-groups/config/people-groups.policies";
import LiveFeedButton from "@/tenant-context/control-live-feed/components/LiveFeedButton";

type Props = {
  title: string
}

const ControlGroupsComponent: FC<Props> = ({ title }) => {
  const { classes } = useControlGroupsStyles();
  const { openPeopleGroupFormModal } = useContext(CreateGroupModalContext);
  const navigate = useNavigate();

  const isWithFullAccess = usePermission([ PeopleGroupsPoliciesConfig.PEOPLE_GROUPS_FULL_ACCESS ]);

  const createStandardGroupHandler = useCallback(() => {
    openPeopleGroupFormModal();
  }, [openPeopleGroupFormModal]);

  const createDynamicGroupHandler = useCallback(() => {
    navigate(RouterConfig.routes.peopleGroupsCreateDynamic);
  }, [navigate]);

  return (

    <div className={ classes.container }>
      <div className={ classes.controlsContainer }>
        <div className={ classes.notificationContainer }>
          <LiveFeedButton/>
        </div>
        <div className={ classes.linkContainer }>
          <Link to="/profile/profile-list" className={ classes.link }><Arrow /> Go to People</Link>
          { isWithFullAccess && (
            <Menu>
              <Menu.Target>
                <Button className={ classes.createButton }>
                  + Create New Group
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item onClick={ createStandardGroupHandler }>
                  Create Standard Group
                </Menu.Item>
                <Menu.Item onClick={ createDynamicGroupHandler }>
                  Create Dynamic Group
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          ) }
        </div>
      </div>
      <div className={ classes.title }>{ title }</div>
      <ControlGroupsTable />
    </div>
  );
};

export default ControlGroupsComponent;
