import { createStyles } from "@mantine/core";
import { MantineTheme } from "@mantine/styles/lib/theme";

export const usePageStyles = createStyles((theme: MantineTheme) => ({
  pageRoot: {
    backgroundColor: theme.colors.neutral[9],
    height: '100vh'
  },
  headerRoot: {
    padding: theme.spacing.xl,
    borderBottom: `1px solid ${theme.colors.neutral[7]}`,
    height: 112,
    minHeight: 112
  },
  bodyRoot: {
    padding: '32px 0px',
    flexGrow: 1,
    overflowY: 'auto'
  },
  cardRoot: {
    background: theme.colors.neutral[8]
  }
}));
