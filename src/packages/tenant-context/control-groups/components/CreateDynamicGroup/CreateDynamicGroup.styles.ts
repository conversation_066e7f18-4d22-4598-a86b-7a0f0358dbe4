import { createStyles } from '@mantine/core';

import { CSS_KEY } from '@/core/styles/mantine/constants';
import { genericColorPalette } from '@/core/styles/mantine/palettes';

export const useCreateDynamicGroupStyles = createStyles((theme) => ({

  mainContent: {
    display: 'flex',
    gap: 4,
    height: 'calc(100vh - 300px)',
    [`.${CSS_KEY}-InputWrapper-root`]: {
      margin: '0 !important'
    }
  },

  leftPanel: {
    flex: 13,
    background: theme.colors.neutral[8],
    overflow: 'auto'
  },

  rightPanel: {
    flex: 10,
    background: theme.colors.neutral[8],
    overflow: 'auto'
  },

  conditionGroupContainer: {
  },

  sectionTitle: {
    color: '#FFFFFF',
    marginBottom: '16px'
  },

  inputField: {
    '& label': {
      color: '#FFFFFF'
    },
    '& input': {
      backgroundColor: '#2A2D31',
      border: '1px solid #576275',
      color: '#FFFFFF'
    }
  },

  filterConfigContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: 16,
    borderRadius: 8,
    background: '#142B3C'
  },

  editInputField: {
    '& label': {
      color: '#FFFFFF'
    },
    '& input': {
      backgroundColor: '#1E3445',
      border: '1px solid #576275',
      color: '#FFFFFF'
    }
  },

  categoryBadge: {
    display: 'flex',
    height: 24,
    minHeight: 24,
    padding: `${theme.spacing.xs} ${theme.spacing.sm}`,
    marginRight: theme.spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
    background: theme.fn.rgba(theme.white, 0.25)
  },

  conditionGroup: {
    display: 'flex',
    padding: theme.spacing.md,
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: theme.spacing.md,
    alignSelf: 'stretch',
    borderRadius: 8,
    background: theme.colors.royalBlue[3]
    // border: '1px solid #576275',
    // borderRadius: '8px'
    // padding: '16px'
    // backgroundColor: '#2A2D31'
  },

  groupHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '16px'
  },

  groupTitle: {
    color: '#FFFFFF',
    fontWeight: 600
  },

  conditionItem: {
    display: 'flex',
    height: 44,
    minWidth: 300,
    minHeight: 44,
    padding: 8,
    alignItems: 'center',
    gap: theme.spacing.xs,
    alignSelf: 'stretch',
    borderRadius: 4,
    background: theme.fn.rgba(theme.white, 0.08)
  },

  conditionText: {
    color: '#FFFFFF',
    cursor: 'pointer',
    flex: 1
  },

  addFilterBox: {
    border: '2px dashed #576275',
    borderRadius: '4px',
    textAlign: 'center',
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'center',
    gap: theme.spacing.xs,
    alignItems: 'center',
    padding: theme.spacing.xs,
    height: 44
  },

  addGroupButton: {
    display: 'flex',
    height: 44,
    minHeight: 44,
    padding: theme.spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.xs,
    alignSelf: 'stretch',
    borderRadius: 4,
    background: genericColorPalette.teal[5]
  },

  divider: {
    '& .mantine-Divider-label': {
      color: '#FFFFFF'
    }
  },

  editPlaceholder: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '200px',
    color: '#576275'
  },

  footer: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '12px'
  },

  cancelButton: {
    borderColor: '#576275',
    color: '#FFFFFF'
  },

  createButton: {
    backgroundColor: '#00D4AA',
    color: '#000000'
  },

  filterButton: {
    borderColor: '#576275',
    color: '#FFFFFF'
  },

  confirmButton: {
    backgroundColor: '#00D4AA',
    color: '#000000'
  },

  highlightedText: {
    color: 'var(--text-secondary-text-tertiary, #649AFE)',
    fontFamily: 'var(--Font-Family-Body, Inter)',
    fontSize: 'inherit',
    fontStyle: 'normal',
    fontWeight: 700,
    lineHeight: '125%'
  },

  emptyFiltersPlaceholder: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
    border: '1px dashed #576275',
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.05)'
  }
}));