import { createContext, ReactNode, useContext, useEffect,useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { RouterConfig } from "@/core/components/Router/Router.config";

import { getConditionOptions, getFieldData, getValueFieldType } from './CreateDynamicGroup.config';
import { ConditionGroup, FilterCondition } from './CreateDynamicGroup.types';

interface CreateDynamicGroupContextType {
  // State
  activeTab: string;
  groupName: string;
  categories: string[];
  conditionGroups: ConditionGroup[];
  selectedFilter: FilterCondition | null;
  editFilterField: string;
  editFilterOperator: string;
  editFilterValues: string[];
  availableValues: string[];
  conditionOptions: Array<{ value: string; label: string }>;
  valueFieldType: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'date_range' | 'number_range';

  // Actions
  setActiveTab: (tab: string) => void;
  setGroupName: (name: string) => void;
  setCategories: (categories: string[]) => void;
  handleBackClick: () => void;
  handleAddConditionGroup: () => void;
  handleAddCondition: (groupId: string) => void;
  handleDeleteCondition: (groupId: string, conditionId: string) => void;
  handleDeleteGroup: (groupId: string) => void;
  handleCopyGroup: (groupId: string) => void;
  handleEditFilter: (condition: FilterCondition) => void;
  handleConfirmFilter: () => void;
  handleCancelFilter: () => void;
  setEditFilterField: (field: string) => void;
  setEditFilterOperator: (operator: string) => void;
  setEditFilterValues: (values: string[]) => void;
}

const CreateDynamicGroupContext = createContext<CreateDynamicGroupContextType | undefined>(undefined);

export const useCreateDynamicGroup = () => {
  const context = useContext(CreateDynamicGroupContext);
  if (!context) {
    throw new Error('useCreateDynamicGroup must be used within CreateDynamicGroupProvider');
  }

  return context;
};

export const CreateDynamicGroupProvider: React.FC = ({ children }) => {
  const navigate = useNavigate();
  
  // State
  const [activeTab, setActiveTab] = useState<string>('conditions');
  const [groupName, setGroupName] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [conditionGroups, setConditionGroups] = useState<ConditionGroup[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<FilterCondition | null>(null);
  const [editFilterField, setEditFilterField] = useState('');
  const [editFilterOperator, setEditFilterOperator] = useState('');
  const [editFilterValues, setEditFilterValues] = useState<string[]>([]);
  const [availableValues, setAvailableValues] = useState<string[]>([]);
  const [conditionOptions, setConditionOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [valueFieldType, setValueFieldType] = useState<'text' | 'number' | 'date' | 'boolean' | 'select' | 'date_range' | 'number_range'>('select');

  // Update available values when field changes
  useEffect(() => {
    const newConditionOptions = getConditionOptions(editFilterField);
    setConditionOptions(newConditionOptions);
    setAvailableValues(getFieldData(editFilterField));
    
    // Reset operator and values when field changes
    if (newConditionOptions.length > 0) {
      setEditFilterOperator(newConditionOptions[0].value);
    }
    setEditFilterValues([]);
  }, [editFilterField]);

  // Update value field type when field or condition changes
  useEffect(() => {
    const newValueFieldType = getValueFieldType(editFilterField, editFilterOperator);
    setValueFieldType(newValueFieldType);
  }, [editFilterField, editFilterOperator]);

  // Business Logic
  const handleBackClick = () => {
    navigate(RouterConfig.routes.peopleGroups);
  };

  const handleAddConditionGroup = () => {
    const newGroup: ConditionGroup = {
      id: (conditionGroups.length + 1).toString(),
      conditions: []
    };

    setConditionGroups([...conditionGroups, newGroup]);
  };

  const handleAddCondition = (groupId: string) => {
    const currentGroup = conditionGroups.find(g => g.id === groupId);
    const newCondition: FilterCondition = {
      id: `${groupId}-${(currentGroup?.conditions?.length || 0) + 1}`,
      field: '',
      operator: '',
      values: []
    };

    setConditionGroups(groups => 
      groups.map(group => 
        group.id === groupId 
          ? {
            ...group,
            conditions: [...group.conditions, newCondition]
          }
          : group));

    // Automatically select the new filter for editing
    setSelectedFilter(newCondition);
    setEditFilterField('');
    setEditFilterOperator('');
    setEditFilterValues([]);
  };

  const handleDeleteCondition = (groupId: string, conditionId: string) => {
    setConditionGroups(groups => 
      groups.map(group => 
        group.id === groupId 
          ? {
            ...group,
            conditions: group.conditions.filter(c => c.id !== conditionId)
          }
          : group));
  };

  const handleDeleteGroup = (groupId: string) => {
    setConditionGroups(groups => groups.filter(group => group.id !== groupId));
  };

  const handleCopyGroup = (groupId: string) => {
    const groupToCopy = conditionGroups.find(g => g.id === groupId);
    if (groupToCopy) {
      const newGroup: ConditionGroup = {
        id: (conditionGroups.length + 1).toString(),
        conditions: [...groupToCopy.conditions]
      };

      setConditionGroups([...conditionGroups, newGroup]);
    }
  };

  const handleEditFilter = (condition: FilterCondition) => {
    setSelectedFilter(condition);
    setEditFilterField(condition.field);
    setEditFilterOperator(condition.operator);
    setEditFilterValues(condition.values);
  };

  const handleConfirmFilter = () => {
    if (selectedFilter) {
      setConditionGroups(groups => 
        groups.map(group => ({
          ...group,
          conditions: group.conditions.map(condition => 
            condition.id === selectedFilter.id 
              ? {
                ...condition,
                field: editFilterField,
                operator: editFilterOperator,
                values: editFilterValues
              }
              : condition)
        })));
      setSelectedFilter(null);
    }
  };

  const handleCancelFilter = () => {
    setSelectedFilter(null);
  };

  const value: CreateDynamicGroupContextType = {
    // State
    activeTab,
    groupName,
    categories,
    conditionGroups,
    selectedFilter,
    editFilterField,
    editFilterOperator,
    editFilterValues,
    availableValues,
    conditionOptions,
    valueFieldType,

    // Actions
    setActiveTab,
    setGroupName,
    setCategories,
    handleBackClick,
    handleAddConditionGroup,
    handleAddCondition,
    handleDeleteCondition,
    handleDeleteGroup,
    handleCopyGroup,
    handleEditFilter,
    handleConfirmFilter,
    handleCancelFilter,
    setEditFilterField,
    setEditFilterOperator,
    setEditFilterValues
  };

  return (
    <CreateDynamicGroupContext.Provider value={ value }>
      { children }
    </CreateDynamicGroupContext.Provider>
  );
}; 